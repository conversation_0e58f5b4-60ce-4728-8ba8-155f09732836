<div>
    <div class="d-flex align-items-center">
        <!-- Area Filter Dropdown -->
        <div class="dropdown" wire:ignore>
            <button class="btn btn-outline-primary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="outside" id="areaFilterButton">
                <i class="ri-filter-3-line me-1"></i>
                <span id="filterButtonText">Filter Areas ({{ count($selectedAreas) }}/{{ count($availableAreas) }})</span>
            </button>
            <div class="dropdown-menu dropdown-menu-end p-3" style="max-height: 400px; overflow-y: auto; min-width: 280px;" id="areaFilterDropdown">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="dropdown-header p-0 m-0">Select Areas</h6>
                    <button type="button" class="btn btn-link btn-sm p-0 text-decoration-none" onclick="areaFilterManager.resetFilter()">
                        <i class="ri-refresh-line me-1"></i>Reset
                    </button>
                </div>

                <!-- Select All/None Toggle -->
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="selectAllAreas" onchange="areaFilterManager.toggleAllAreas()">
                    <label class="form-check-label fw-bold" for="selectAllAreas" id="selectAllLabel">
                        Select All
                    </label>
                </div>

                <hr class="dropdown-divider my-2">

                <!-- Individual Area Checkboxes -->
                <div class="area-checkboxes" style="max-height: 200px; overflow-y: auto;" id="areaCheckboxContainer">
                    @foreach($availableAreas as $area)
                    <div class="form-check mb-1">
                        <input class="form-check-input area-checkbox" type="checkbox" id="area_{{ $area }}"
                               value="{{ $area }}"
                               {{ in_array($area, $tempSelectedAreas) ? 'checked' : '' }}
                               onchange="areaFilterManager.toggleArea('{{ $area }}')">
                        <label class="form-check-label" for="area_{{ $area }}">
                            {{ $area }}
                        </label>
                    </div>
                    @endforeach
                </div>

                <hr class="dropdown-divider my-2">

                <!-- Action Buttons -->
                <div class="d-flex justify-content-between gap-2">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="dropdown" onclick="areaFilterManager.cancelFilter()">
                        <i class="ri-close-line me-1"></i>Cancel
                    </button>
                    <button type="button" class="btn btn-primary btn-sm" data-bs-dismiss="dropdown" onclick="areaFilterManager.applyFilter()">
                        <i class="ri-check-line me-1"></i>Apply Filter
                    </button>
                </div>

                <!-- Loading indicator -->
                <div wire:loading class="text-center mt-2">
                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* Custom styles for the area filter */
        .dropdown-menu {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .form-check-input:checked {
            background-color: var(--bs-primary);
            border-color: var(--bs-primary);
        }

        .form-check-label {
            cursor: pointer;
            user-select: none;
        }

        .area-checkboxes {
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 0.375rem;
            padding: 0.5rem;
            background-color: rgba(0, 0, 0, 0.02);
        }

        .btn-link {
            color: var(--bs-primary);
        }

        .btn-link:hover {
            color: var(--bs-primary);
            text-decoration: underline !important;
        }
    </style>

    <script>
        window.areaFilterManager = {
            availableAreas: @json($availableAreas),
            selectedAreas: @json($selectedAreas),
            tempSelectedAreas: [...@json($tempSelectedAreas)],

            init() {
                this.updateUI();

                // Prevent dropdown from closing when clicking inside
                document.getElementById('areaFilterDropdown').addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            },

            updateUI() {
                const selectedCount = this.tempSelectedAreas.length;
                const totalCount = this.availableAreas.length;
                const showAllAreas = selectedCount === totalCount;

                // Update button text
                document.getElementById('filterButtonText').textContent =
                    `Filter Areas (${selectedCount}/${totalCount})`;

                // Update select all checkbox
                const selectAllCheckbox = document.getElementById('selectAllAreas');
                selectAllCheckbox.checked = showAllAreas;

                // Update select all label
                document.getElementById('selectAllLabel').textContent =
                    showAllAreas ? 'Deselect All' : 'Select All';

                // Update individual checkboxes
                document.querySelectorAll('.area-checkbox').forEach(checkbox => {
                    checkbox.checked = this.tempSelectedAreas.includes(checkbox.value);
                });
            },

            toggleArea(area) {
                const index = this.tempSelectedAreas.indexOf(area);
                if (index > -1) {
                    this.tempSelectedAreas.splice(index, 1);
                } else {
                    this.tempSelectedAreas.push(area);
                }
                this.updateUI();
            },

            toggleAllAreas() {
                const selectAllCheckbox = document.getElementById('selectAllAreas');
                if (selectAllCheckbox.checked) {
                    this.tempSelectedAreas = [...this.availableAreas];
                } else {
                    this.tempSelectedAreas = [];
                }
                this.updateUI();
            },

            applyFilter() {
                // Call Livewire method to apply the filter
                @this.call('applyFilterWithAreas', this.tempSelectedAreas);
            },

            cancelFilter() {
                // Reset temp selection to current selection
                this.tempSelectedAreas = [...this.selectedAreas];
                this.updateUI();
            },

            resetFilter() {
                // Reset to all areas
                this.tempSelectedAreas = [...this.availableAreas];
                this.updateUI();
                @this.call('resetFilter');
            }
        };

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            areaFilterManager.init();
        });

        // Re-initialize after Livewire updates
        document.addEventListener('livewire:updated', function() {
            // Update the data from server
            areaFilterManager.availableAreas = @json($availableAreas);
            areaFilterManager.selectedAreas = @json($selectedAreas);
            areaFilterManager.tempSelectedAreas = [...@json($tempSelectedAreas)];
            areaFilterManager.updateUI();
        });
    </script>
</div>