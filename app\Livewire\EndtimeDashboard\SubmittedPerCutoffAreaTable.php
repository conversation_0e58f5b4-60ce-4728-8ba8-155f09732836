<?php

namespace App\Livewire\EndtimeDashboard;

use Livewire\Component;
use App\Models\ViProdEndtimeSubmitted;
use App\Models\ViCapaRef;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;

class SubmittedPerCutoffAreaTable extends Component
{
    public $areaData = [];
    public $cutoffNames = [];
    public $areaCodes = [];
    public $totalTarget = 0;
    public $totalCutoffs = [];
    public $selectedAreas = [];
    public $availableAreas = [];
    public $showAllAreas = true;

    protected $listeners = [
        'dateChanged' => 'refreshData',
        'cutoffChanged' => 'refreshData',
        'worktypeChanged' => 'refreshData',
        'lottypeChanged' => 'refreshData',
        'refreshData' => 'refreshData',
        'areaFilterChanged' => 'updateAreaFilter'
    ];

    public function mount()
    {
        $this->initializeAreaFilter();
        $this->refreshData();
    }

    /**
     * Initialize area filter with all available areas
     */
    public function initializeAreaFilter()
    {
        $this->availableAreas = $this->getUniqueAreas();
        $this->selectedAreas = $this->availableAreas; // Show all areas by default
    }

    /**
     * Update area filter from shared filter component
     */
    public function updateAreaFilter($data)
    {
        $this->selectedAreas = $data['selectedAreas'] ?? [];
        Log::info("SubmittedPerCutoffAreaTable - Updated area filter: " . implode(', ', $this->selectedAreas));
        $this->refreshData();
    }

    /**
     * Get the current date from session
     *
     * @return string
     */
    protected function getDate()
    {
        return session('date', now()->format('Y-m-d'));
    }

    /**
     * Get the current worktype from session
     *
     * @return string
     */
    protected function getWorktype()
    {
        return session('worktype', 'all');
    }

    /**
     * Get the current lottype from session
     *
     * @return string
     */
    protected function getLottype()
    {
        return session('lottype', 'all');
    }

    /**
     * Get unique areas from the database
     *
     * @return array
     */
    protected function getUniqueAreas()
    {
        try {
            // Get unique areas from vi_capa_ref table
            $areas = ViCapaRef::whereNotNull('area')
                ->where('area', '!=', '')
                ->distinct()
                ->pluck('area')
                ->sort()
                ->values()
                ->toArray();

            Log::info("Found unique areas for cutoff table: " . implode(', ', $areas));
            
            return $areas;
        } catch (\Exception $e) {
            Log::error("Error getting unique areas for cutoff table: " . $e->getMessage());
            // Return default areas if query fails
            return ['AREA1', 'AREA2', 'AREA3'];
        }
    }

    public function refreshData()
    {
        try {
            // Get filter values from session
            $date = $this->getDate();
            $worktype = $this->getWorktype();
            $lottype = $this->getLottype();

            Log::info("SubmittedPerCutoffAreaTable refreshData - Using date: '$date', worktype: '$worktype', lottype: '$lottype'");

            // Use selected areas for filtering, or all areas if none selected
            $this->areaCodes = !empty($this->selectedAreas) ? $this->selectedAreas : $this->getUniqueAreas();

            // Define cutoff times and their display names
            $this->cutoffNames = [
                '00:00~04:00' => '4AM',
                '04:00~07:00' => '7AM',
                '07:00~12:00' => '12NN',
                '12:00~16:00' => '4PM',
                '16:00~19:00' => '7PM',
                '19:00~00:00' => '12MN'
            ];

            // Initialize the area data structure
            $this->areaData = [];
            $this->totalTarget = 0;
            $this->totalCutoffs = [];

            // Initialize total cutoffs array
            foreach ($this->cutoffNames as $cutoffTime => $cutoffDisplay) {
                $this->totalCutoffs[$cutoffTime] = [
                    'qty' => 0,
                    'percentage' => 0
                ];
            }

            // Get target data for each area
            $targetData = $this->getTargetDataByArea($worktype, $this->areaCodes);

            // For each area, get submitted data for each cutoff
            foreach ($this->areaCodes as $index => $areaCode) {
                $areaTarget = $targetData[$index] ?? 0;
                $this->totalTarget += $areaTarget;

                $areaInfo = [
                    'area' => $areaCode,
                    'target' => $areaTarget,
                    'cutoffs' => []
                ];

                // Get submitted data for each cutoff
                foreach ($this->cutoffNames as $cutoffTime => $cutoffDisplay) {
                    $submitted = $this->getSubmittedDataForAreaCutoff($date, $areaCode, $cutoffTime, $worktype, $lottype);
                    $percentage = $areaTarget > 0 ? round(($submitted / $areaTarget) * 100, 1) : 0;

                    $areaInfo['cutoffs'][$cutoffTime] = [
                        'qty' => $submitted,
                        'percentage' => $percentage
                    ];

                    // Add to totals
                    $this->totalCutoffs[$cutoffTime]['qty'] += $submitted;
                }

                $this->areaData[] = $areaInfo;
            }

            // Calculate total percentages
            foreach ($this->cutoffNames as $cutoffTime => $cutoffDisplay) {
                $this->totalCutoffs[$cutoffTime]['percentage'] =
                    $this->totalTarget > 0
                    ? round(($this->totalCutoffs[$cutoffTime]['qty'] / $this->totalTarget) * 100, 1)
                    : 0;
            }
        } catch (\Exception $e) {
            Log::error("Error in SubmittedPerCutoffAreaTable refreshData: " . $e->getMessage());
            Log::error("Stack trace: " . $e->getTraceAsString());
        }
    }

    /**
     * Get target data by area from vi_capa_ref
     *
     * @param string $worktype
     * @param array $areaCodes
     * @return array
     */
    protected function getTargetDataByArea($worktype, $areaCodes)
    {
        $result = [];

        try {
            foreach ($areaCodes as $areaCode) {
                // Query the database using the model with worktype and area filters
                $query = ViCapaRef::query();

                // Apply worktype filtering using the scope in ViCapaRef model
                $query->filterByWorktype($worktype);

                // Filter by area
                $query->where('area', $areaCode);

                // Sum the actual_capa column to get total capacity for this area
                $areaCapacity = $query->sum('actual_capa');

                // Count the number of machines for this area
                $machineCount = $query->count('mc_no');

                Log::info("Area $areaCode target capacity: $areaCapacity from $machineCount machines");

                // For specific cutoff times, divide by 6 (assuming 6 equal cutoff periods)
                $areaCapacity = $areaCapacity / 6;

                // Add to result array
                $result[] = (int)$areaCapacity;
            }

            return $result;
        } catch (\Exception $e) {
            Log::error("Error getting target data by area: " . $e->getMessage());
            return array_fill(0, count($areaCodes), 0);
        }
    }

    /**
     * Get submitted data for a specific area and cutoff
     *
     * @param string $date
     * @param string $areaCode
     * @param string $cutoffTime
     * @param string $worktype
     * @param string $lottype
     * @return int
     */
    protected function getSubmittedDataForAreaCutoff($date, $areaCode, $cutoffTime, $worktype, $lottype)
    {
        try {
            // Create base query
            $query = ViProdEndtimeSubmitted::byDate($date)
                ->byWorkType($worktype)
                ->byLotType($lottype)
                ->where('cutoff_time', $cutoffTime)
                ->where('area', $areaCode)
                ->whereRaw('LOWER(status) = ?', ['submitted']);

            // Sum the lot_qty column to get total submitted
            $total = $query->sum('lot_qty');

            return (int)$total;
        } catch (\Exception $e) {
            Log::error("Error getting submitted data for area $areaCode and cutoff $cutoffTime: " . $e->getMessage());
            return 0;
        }
    }

    public function render()
    {
        return view('livewire.endtime-dashboard.submitted-per-cutoff-area-table');
    }
}