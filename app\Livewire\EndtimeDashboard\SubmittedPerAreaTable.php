<?php

namespace App\Livewire\EndtimeDashboard;

use Livewire\Component;
use App\Models\ViProdEndtimeSubmitted;
use App\Models\ViCapaRef;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;

class SubmittedPerAreaTable extends Component
{
    public $targetData = [];
    public $submittedData = [];
    public $percentages = [];
    public $shortages = [];
    public $date;
    public $cutoff = 'total';
    public $cutoffDisplay;
    public $selectedAreas = [];
    public $availableAreas = [];
    public $showAllAreas = true;

    protected $listeners = [
        'dateChanged' => 'refreshData',
        'cutoffChanged' => 'refreshData',
        'worktypeChanged' => 'refreshData',
        'lottypeChanged' => 'refreshData',
        'refreshData' => 'refreshData',
        'areaFilterChanged' => 'updateAreaFilter'
    ];

    public function mount()
    {
        $this->date = now()->format('M d, Y');
        $this->initializeAreaFilter();
        $this->refreshData();
    }

    /**
     * Initialize area filter with all available areas
     */
    public function initializeAreaFilter()
    {
        $this->availableAreas = $this->getUniqueAreas();
        $this->selectedAreas = $this->availableAreas; // Show all areas by default
    }

    /**
     * Update area filter from shared filter component
     */
    public function updateAreaFilter($data)
    {
        $this->selectedAreas = $data['selectedAreas'] ?? [];
        Log::info("SubmittedPerAreaTable - Updated area filter: " . implode(', ', $this->selectedAreas));
        $this->refreshData();
    }

    /**
     * Get the current date from session
     *
     * @return string
     */
    protected function getDate()
    {
        return session('date', now()->format('Y-m-d'));
    }

    /**
     * Get the current cutoff from session
     *
     * @return string
     */
    protected function getCutoff()
    {
        return session('cutoff', 'all');
    }

    /**
     * Get the current worktype from session
     *
     * @return string
     */
    protected function getWorktype()
    {
        return session('worktype', 'all');
    }

    /**
     * Get the current lottype from session
     *
     * @return string
     */
    protected function getLottype()
    {
        return session('lottype', 'all');
    }

    public function refreshData()
    {
        try {
            // Get filter values from session
            $date = $this->getDate();
            $this->date = date('M d, Y', strtotime($date));
            $cutoff = $this->getCutoff();
            $worktype = $this->getWorktype();
            $lottype = $this->getLottype();

            // Set cutoff display text
            $this->setCutoffDisplay($cutoff);

            Log::info("SubmittedPerAreaTable refreshData - Using date: '$date', cutoff: '$cutoff', worktype: '$worktype', lottype: '$lottype'");

            // Use selected areas for filtering, or all areas if none selected
            $areaCodes = !empty($this->selectedAreas) ? $this->selectedAreas : $this->getUniqueAreas();

            // Get target data by area from vi_capa_ref
            $this->targetData = $this->getTargetDataByArea($worktype, $cutoff, $areaCodes);

            // Get submitted data by area from vi_prod_endtime_submitted
            $this->submittedData = $this->getSubmittedDataByArea($date, $cutoff, $worktype, $lottype, $areaCodes);

            // Calculate percentages and shortages
            $this->percentages = [];
            $this->shortages = [];

            foreach ($areaCodes as $index => $areaCode) {
                $target = $this->targetData[$index] ?? 1; // Avoid division by zero
                $submitted = $this->submittedData[$index] ?? 0;

                $this->percentages[] = $target > 0 ? round(($submitted / $target) * 100, 1) : 0;
                $this->shortages[] = max(0, $target - $submitted);
            }

        } catch (\Exception $e) {
            Log::error("Error in SubmittedPerAreaTable refreshData: " . $e->getMessage());
            Log::error("Stack trace: " . $e->getTraceAsString());
        }
    }

    /**
     * Get unique areas from the database
     *
     * @return array
     */
    protected function getUniqueAreas()
    {
        try {
            // Get unique areas from vi_capa_ref table
            $areas = ViCapaRef::whereNotNull('area')
                ->where('area', '!=', '')
                ->distinct()
                ->pluck('area')
                ->sort()
                ->values()
                ->toArray();

            Log::info("Found unique areas: " . implode(', ', $areas));
            
            return $areas;
        } catch (\Exception $e) {
            Log::error("Error getting unique areas: " . $e->getMessage());
            // Return default areas if query fails
            return ['AREA1', 'AREA2', 'AREA3'];
        }
    }

    /**
     * Get target data by area from vi_capa_ref
     *
     * @param string $worktype
     * @param string $cutoff
     * @param array $areaCodes
     * @return array
     */
    protected function getTargetDataByArea($worktype, $cutoff, $areaCodes)
    {
        $result = [];

        try {
            foreach ($areaCodes as $areaCode) {
                // Query the database using the model with worktype and area filters
                $query = ViCapaRef::query();

                // Apply worktype filtering using the scope in ViCapaRef model
                $query->filterByWorktype($worktype);

                // Filter by area
                $query->where('area', $areaCode);

                // Sum the actual_capa column to get total capacity for this area
                $areaCapacity = $query->sum('actual_capa');

                // Count the number of machines for this area
                $machineCount = $query->count('mc_no');

                Log::info("Area $areaCode target capacity: $areaCapacity from $machineCount machines");

                // Adjust target based on cutoff
                if ($cutoff === 'day') {
                    $areaCapacity = $areaCapacity * 0.5; // 50% of total capacity for day shift
                } elseif ($cutoff === 'night') {
                    $areaCapacity = $areaCapacity * 0.5; // 50% of total capacity for night shift
                } elseif ($cutoff !== 'all') {
                    // For specific cutoff times, divide by 6 (assuming 6 equal cutoff periods)
                    $areaCapacity = $areaCapacity / 6;
                }

                // Add to result array
                $result[] = (int)$areaCapacity;
            }

            return $result;
        } catch (\Exception $e) {
            Log::error("Error getting target data by area: " . $e->getMessage());
            return array_fill(0, count($areaCodes), 10000000); // Default target value
        }
    }

    /**
     * Get submitted data by area from vi_prod_endtime_submitted
     *
     * @param string $date
     * @param string $cutoff
     * @param string $worktype
     * @param string $lottype
     * @param array $areaCodes
     * @return array
     */
    protected function getSubmittedDataByArea($date, $cutoff, $worktype, $lottype, $areaCodes)
    {
        $result = [];

        try {
            foreach ($areaCodes as $areaCode) {
                // Create base query
                $query = ViProdEndtimeSubmitted::byDate($date)
                    ->byWorkType($worktype)
                    ->byLotType($lottype)
                    ->byCutoff($cutoff)
                    ->byStatus('submitted');

                // Filter by area
                $query->where('area', $areaCode);

                // Sum the lot_qty column to get total submitted for this area
                $areaTotal = $query->sum('lot_qty');

                // Add to result array
                $result[] = (int)$areaTotal;
            }

            return $result;
        } catch (\Exception $e) {
            Log::error("Error getting submitted data by area: " . $e->getMessage());
            return array_fill(0, count($areaCodes), 0);
        }
    }

    public function setCutoff($value)
    {
        $this->cutoff = $value;
        $this->refreshData();
    }

    /**
     * Set the cutoff display text based on the cutoff value
     *
     * @param string $cutoff
     * @return void
     */
    protected function setCutoffDisplay($cutoff)
    {
        switch ($cutoff) {
            case 'total':
                $this->cutoffDisplay = 'Total';
                break;
            case 'day':
                $this->cutoffDisplay = 'Day Shift';
                break;
            case 'night':
                $this->cutoffDisplay = 'Night Shift';
                break;
            case '00:00~04:00':
                $this->cutoffDisplay = '4AM Cutoff';
                break;
            case '04:00~07:00':
                $this->cutoffDisplay = '7AM Cutoff';
                break;
            case '07:00~12:00':
                $this->cutoffDisplay = '12NN Cutoff';
                break;
            case '12:00~16:00':
                $this->cutoffDisplay = '4PM Cutoff';
                break;
            case '16:00~19:00':
                $this->cutoffDisplay = '7PM Cutoff';
                break;
            case '19:00~00:00':
                $this->cutoffDisplay = '12MN Cutoff';
                break;
            default:
                $this->cutoffDisplay = 'All Cutoffs';
                break;
        }
    }

    public function render()
    {
        return view('livewire.endtime-dashboard.submitted-per-area-table');
    }
}