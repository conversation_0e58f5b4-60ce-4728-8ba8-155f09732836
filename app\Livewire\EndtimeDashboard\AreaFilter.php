<?php

namespace App\Livewire\EndtimeDashboard;

use Livewire\Component;
use App\Models\ViCapaRef;
use Illuminate\Support\Facades\Log;

class AreaFilter extends Component
{
    public $selectedAreas = [];
    public $availableAreas = [];
    public $tempSelectedAreas = []; // Temporary selection before applying
    public $showAllAreas = true;

    protected $listeners = [
        'refreshData' => 'initializeAreaFilter'
    ];

    public function mount()
    {
        $this->initializeAreaFilter();
    }

    /**
     * Initialize area filter with all available areas
     */
    public function initializeAreaFilter()
    {
        $this->availableAreas = $this->getUniqueAreas();
        $this->selectedAreas = $this->availableAreas; // Show all areas by default
        $this->tempSelectedAreas = $this->selectedAreas; // Initialize temp selection
        $this->showAllAreas = true;
    }

    /**
     * Get unique areas from the database
     */
    protected function getUniqueAreas()
    {
        try {
            $areas = ViCapaRef::whereNotNull('area')
                ->where('area', '!=', '')
                ->distinct()
                ->pluck('area')
                ->sort()
                ->values()
                ->toArray();

            Log::info("AreaFilter - Found unique areas: " . implode(', ', $areas));
            
            return $areas;
        } catch (\Exception $e) {
            Log::error("Error getting unique areas in AreaFilter: " . $e->getMessage());
            return ['AREA1', 'AREA2', 'AREA3'];
        }
    }

    /**
     * Toggle area selection in temporary array (doesn't apply immediately)
     */
    public function toggleTempArea($area)
    {
        if (in_array($area, $this->tempSelectedAreas)) {
            $this->tempSelectedAreas = array_diff($this->tempSelectedAreas, [$area]);
        } else {
            $this->tempSelectedAreas[] = $area;
        }
        
        // Update showAllAreas flag
        $this->showAllAreas = count($this->tempSelectedAreas) === count($this->availableAreas);
    }

    /**
     * Toggle all areas selection in temporary array
     */
    public function toggleAllTempAreas()
    {
        if ($this->showAllAreas) {
            $this->tempSelectedAreas = [];
            $this->showAllAreas = false;
        } else {
            $this->tempSelectedAreas = $this->availableAreas;
            $this->showAllAreas = true;
        }
    }

    /**
     * Apply the temporary selection to actual selection and dispatch event
     */
    public function applyFilter()
    {
        $this->selectedAreas = $this->tempSelectedAreas;

        Log::info("AreaFilter - Applied filter with areas: " . implode(', ', $this->selectedAreas));

        // Dispatch event to both area tables
        $this->dispatch('areaFilterChanged', ['selectedAreas' => $this->selectedAreas]);
    }

    /**
     * Apply filter with areas array from Alpine.js
     */
    public function applyFilterWithAreas($areas)
    {
        $this->tempSelectedAreas = $areas;
        $this->selectedAreas = $areas;

        Log::info("AreaFilter - Applied filter with areas from Alpine: " . implode(', ', $this->selectedAreas));

        // Dispatch event to both area tables
        $this->dispatch('areaFilterChanged', ['selectedAreas' => $this->selectedAreas]);
    }

    /**
     * Cancel changes and revert to current selection
     */
    public function cancelFilter()
    {
        $this->tempSelectedAreas = $this->selectedAreas;
        $this->showAllAreas = count($this->selectedAreas) === count($this->availableAreas);
    }

    /**
     * Reset filter to show all areas
     */
    public function resetFilter()
    {
        $this->selectedAreas = $this->availableAreas;
        $this->tempSelectedAreas = $this->availableAreas;
        $this->showAllAreas = true;
        
        // Dispatch event to both area tables
        $this->dispatch('areaFilterChanged', ['selectedAreas' => $this->selectedAreas]);
    }

    public function render()
    {
        return view('livewire.endtime-dashboard.area-filter');
    }
}