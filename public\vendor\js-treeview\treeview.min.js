!function(e){"use strict";!function(t,n){"function"==typeof e&&e.amd?e(n):"object"==typeof exports?module.exports=n():t.TreeView=n()}(window,function(){return function(){function e(e){try{return e instanceof HTMLElement}catch(t){return"object"==typeof e&&1===e.nodeType&&"object"==typeof e.style&&"object"==typeof e.ownerDocument}}function t(e,t,n){var a,o=e.length;for(a=0;a<o;a+=1)t.call(n,e[a],a)}function n(e,n){var a=[].slice.call(arguments,2);if(!(r.indexOf(n)>-1))throw new Error(n+" event cannot be found on TreeView.");e.handlers[n]&&e.handlers[n]instanceof Array&&t(e.handlers[n],function(e){window.setTimeout(function(){e.callback.apply(e.context,a)},0)})}function a(a){var o,r=e(a.node)?a.node:document.getElementById(a.node),l=[],d=function(e){var n=document.createElement("div"),a=document.createElement("div"),o=document.createElement("div"),r=document.createElement("div");if(n.setAttribute("class","tree-leaf"),a.setAttribute("class","tree-leaf-content"),a.setAttribute("data-item",JSON.stringify(e)),o.setAttribute("class","tree-leaf-text"),o.textContent=e.name,r.setAttribute("class","tree-expando "+(e.expanded?"expanded":"")),r.textContent=e.expanded?"-":"+",a.appendChild(r),a.appendChild(o),n.appendChild(a),e.children&&e.children.length>0){var l=document.createElement("div");l.setAttribute("class","tree-child-leaves"),t(e.children,function(e){var t=d(e);l.appendChild(t)}),e.expanded||l.classList.add("hidden"),n.appendChild(l)}else r.classList.add("hidden");return n};t(a.data,function(e){l.push(d.call(a,e))}),r.innerHTML=l.map(function(e){return e.outerHTML}).join(""),o=function(e){var t=(e.target||e.currentTarget).parentNode,o=JSON.parse(t.getAttribute("data-item")),r=t.parentNode.querySelector(".tree-child-leaves");r?r.classList.contains("hidden")?a.expand(t,r):a.collapse(t,r):n(a,"select",{target:e,data:o})},t(r.querySelectorAll(".tree-leaf-text"),function(e){e.onclick=o}),t(r.querySelectorAll(".tree-expando"),function(e){e.onclick=o})}function o(e,t){this.handlers={},this.node=t,this.data=e,a(this)}var r=["expand","expandAll","collapse","collapseAll","select"];return o.prototype.expand=function(e,t,a){e.querySelector(".tree-expando").textContent="-",t.classList.remove("hidden"),a||n(this,"expand",{target:e,leaves:t})},o.prototype.expandAll=function(){var e=this;t(document.getElementById(e.node).querySelectorAll(".tree-expando"),function(t){var n=t.parentNode,a=n.parentNode.querySelector(".tree-child-leaves");n&&a&&n.hasAttribute("data-item")&&e.expand(n,a,!0)}),n(this,"expandAll",{})},o.prototype.collapse=function(e,t,a){e.querySelector(".tree-expando").textContent="+",t.classList.add("hidden"),a||n(this,"collapse",{target:e,leaves:t})},o.prototype.collapseAll=function(){var e=this;t(document.getElementById(e.node).querySelectorAll(".tree-expando"),function(t){var n=t.parentNode,a=n.parentNode.querySelector(".tree-child-leaves");n&&a&&n.hasAttribute("data-item")&&e.collapse(n,a,!0)}),n(this,"collapseAll",{})},o.prototype.on=function(e,t,n){if(!(r.indexOf(e)>-1))throw new Error(e+" is not supported by TreeView.");this.handlers[e]||(this.handlers[e]=[]),this.handlers[e].push({callback:t,context:n})},o.prototype.off=function(e,t){var n,a=!1;this.handlers[e]instanceof Array&&(this.handlers[e].forEach(function(e,o){n=o,e.callback!==t||a||(a=!0)}),a&&this.handlers[e].splice(n,1))},o}()})}(window.define);
//# sourceMappingURL=dist/treeview.min.js.map